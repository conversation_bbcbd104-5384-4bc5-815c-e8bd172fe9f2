#!/bin/bash

echo "========================================"
echo "Wedding Invitation System Setup"
echo "========================================"
echo

echo "Installing Composer dependencies..."
composer install
if [ $? -ne 0 ]; then
    echo "Error: Failed to install Composer dependencies"
    exit 1
fi

echo
echo "Installing NPM dependencies..."
npm install
if [ $? -ne 0 ]; then
    echo "Error: Failed to install NPM dependencies"
    exit 1
fi

echo
echo "Copying environment file..."
if [ ! -f .env ]; then
    cp .env.example .env
    echo "Environment file created. Please configure your database settings in .env"
else
    echo "Environment file already exists."
fi

echo
echo "Generating application key..."
php artisan key:generate

echo
echo "Running database migrations..."
php artisan migrate
if [ $? -ne 0 ]; then
    echo "Error: Failed to run migrations. Please check your database configuration."
    exit 1
fi

echo
echo "Seeding database with initial data..."
php artisan db:seed

echo
echo "Creating storage link..."
php artisan storage:link

echo
echo "Creating required directories..."
mkdir -p public/img
mkdir -p public/galeri
mkdir -p public/story

echo
echo "Setting permissions..."
chmod -R 775 storage
chmod -R 775 bootstrap/cache
chmod -R 775 public/img
chmod -R 775 public/galeri
chmod -R 775 public/story

echo
echo "========================================"
echo "Setup completed successfully!"
echo "========================================"
echo
echo "Default admin credentials:"
echo "Email: <EMAIL>"
echo "Password: password123"
echo
echo "You can now run: php artisan serve"
echo "Then visit: http://localhost:8000"
echo
