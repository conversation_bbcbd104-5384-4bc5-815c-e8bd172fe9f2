<?php

namespace Database\Seeders;

use App\Models\Pengantin;
use App\Models\Hadiah;
use App\Models\Live;
use Illuminate\Database\Seeder;

class WeddingDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Create default pengantin data
        Pengantin::create([
            'nama_pria' => '<PERSON>',
            'nama_lengkap_pria' => '<PERSON>',
            'ortu_pria' => 'Bapak & Ibu Doe',
            'nama_ayah_pria' => 'Mr. <PERSON>e',
            'nama_ibu_pria' => 'Mrs. <PERSON>',
            'nama_wanita' => '<PERSON>',
            'nama_lengkap_wanita' => '<PERSON>',
            'ortu_wanita' => '<PERSON><PERSON><PERSON> & <PERSON><PERSON>',
            'nama_ayah_wanita' => 'Mr. <PERSON>',
            'nama_ibu_wanita' => 'Mrs. <PERSON>',
            'alamat_lokasi' => 'Gedung Pernikahan, Jl. Contoh No. 123, Jakarta',
            'link_maps' => 'https://maps.google.com',
            'embed_maps' => '<iframe src="https://www.google.com/maps/embed" width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy"></iframe>',
        ]);

        // Create default hadiah data
        Hadiah::create([
            'nama_bank' => 'Bank Central Asia',
            'nama_pemilik' => 'John Doe',
            'no_rekening' => '**********',
            'catatan' => 'Terima kasih atas doa dan hadiah yang diberikan.',
        ]);

        // Create default live data
        Live::create([
            'judul' => 'Live Streaming Pernikahan',
            'link' => 'https://youtube.com/live',
            'embed' => '<iframe width="560" height="315" src="https://www.youtube.com/embed/live" frameborder="0" allowfullscreen></iframe>',
        ]);
    }
}
