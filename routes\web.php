<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\RsvpController;
use App\Http\Controllers\WishController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
*/

// Public Routes
Route::get('/', [HomeController::class, 'index'])->name('home');

// Authentication Routes
Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
Route::post('/login', [AuthController::class, 'login']);
Route::get('/register', [AuthController::class, 'showRegister'])->name('register');
Route::post('/register', [AuthController::class, 'register']);
Route::get('/logout', [AuthController::class, 'logout'])->name('logout');

// Public RSVP and Wishes
Route::post('/rsvp', [RsvpController::class, 'store'])->name('rsvp.store');
Route::post('/wishes', [WishController::class, 'storeFromIndex'])->name('wishes.store');

// Admin Protected Routes
Route::middleware('admin.auth')->group(function () {
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Profile Management
    Route::get('/dashboard/profile', [AuthController::class, 'profile'])->name('profile');
    Route::post('/dashboard/profile', [AuthController::class, 'updateProfile']);

    // RSVP Management
    Route::get('/tambah_rsvp', [RsvpController::class, 'create'])->name('rsvp.create');
    Route::post('/proses_rsvp', [RsvpController::class, 'store'])->name('rsvp.admin.store');
    Route::get('/edit_rsvp/{id}', [RsvpController::class, 'edit'])->name('rsvp.edit');
    Route::post('/edit_rsvp/{id}', [RsvpController::class, 'update'])->name('rsvp.update');
    Route::get('/hapus_rsvp/{id}', [RsvpController::class, 'destroy'])->name('rsvp.destroy');

    // Wishes Management
    Route::get('/dashboard/wishes', [WishController::class, 'index'])->name('wishes.index');
    Route::post('/dashboard/wishes', [WishController::class, 'store'])->name('wishes.admin.store');
    Route::get('/hapus_wish/{id}', [WishController::class, 'destroy'])->name('wishes.destroy');

    // Wedding Data Management
    Route::post('/dashboard/pengantin', [DashboardController::class, 'updatePengantin'])->name('pengantin.update');
    Route::post('/dashboard/lokasi', [DashboardController::class, 'updateLokasi'])->name('lokasi.update');
    Route::post('/dashboard/galeri', [DashboardController::class, 'uploadGaleri'])->name('galeri.upload');
    Route::post('/dashboard/galeri/delete/{id}', [DashboardController::class, 'deleteGaleri'])->name('galeri.delete');
    Route::post('/dashboard/story', [DashboardController::class, 'storeStory'])->name('story.store');
    Route::post('/dashboard/story/delete/{id}', [DashboardController::class, 'deleteStory'])->name('story.delete');
    Route::post('/dashboard/hadiah', [DashboardController::class, 'updateHadiah'])->name('hadiah.update');
    Route::post('/dashboard/live', [DashboardController::class, 'updateLive'])->name('live.update');
});