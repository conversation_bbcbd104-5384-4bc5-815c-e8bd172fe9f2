<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Undan<PERSON></title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Sacramento&family=Work+Sans:wght@100;300;400;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="countdown/simplyCountdown.theme.default.css" />
  <script src="countdown/simplyCountdown.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">
  <link rel="stylesheet" href="style.css">
  <link rel="icon" type="image/png" href="{{ asset('favicon.png') }}">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/glightbox/dist/css/glightbox.min.css" />
</head>

<body>

  <section id="hero" class="hero is-fullheight is-primary is-bold has-text-centered" style="background-image: url('img/bg_wed1.jpg'); background-size: cover; background-position: center; background-repeat: no-repeat;">
    <div class="hero-body">
      <div class="container">
        <h4>Kepada <span>Bapak/Ibu/Saudara/i, </span></h4>
        <h1 class="title" style="line-height: 1.5; color: #aedcee;">{{ $pengantin->nama_pria }} & {{ $pengantin->nama_wanita }}</h1> 
        <p class="subtitle">Akan melangsungkan resepsi pernikahan dalam:</p>
        <div class="simply-countdown"></div>
        <a href="#acara" class="button is-large is-primary is-inverted is-outlined mt-4" onClick="enableScroll()">Lihat Undangan</a>
      </div>
    </div>
  </section>
  
 
  <nav class="navbar is-transparent is-fixed-top">
    <div class="container">
      <div class="navbar-brand">
        <a class="navbar-item" href="#" style="font-family: 'Sacramento', cursive; font-weight: bold; font-size: 2rem;">{{ $pengantin->nama_pria }} & {{ $pengantin->nama_wanita }}</a>
        <a role="button" class="navbar-burger" aria-label="menu" aria-expanded="false" data-target="navbarBasicExample">
          <span aria-hidden="true"></span>
          <span aria-hidden="true"></span>
          <span aria-hidden="true"></span>
        </a>
      </div>
      <div id="navbarBasicExample" class="navbar-menu">
        <div class="navbar-end">
          <a class="navbar-item" href="#acara">Acara</a>
          <a class="navbar-item" href="#home">Mempelai</a>
          <a class="navbar-item" href="#info">Lokasi</a>
          <a class="navbar-item" href="#story">Story</a>
          <a class="navbar-item" href="#gallery">Galeri</a>
          <a class="navbar-item" href="#rsvp">RSVP</a>
          <a class="navbar-item" href="#wishes">Doa & Ucapan</a>
          <a class="navbar-item" href="#gifts">Traktir</a>
          <a class="navbar-item" href="#live">Live</a>
        </div>
      </div>
    </div>
  </nav>

  <div class="divider">
    <img src="img/dividerblue.png" alt="Divider" class="animated-divider" style="width: 100%; margin: 0rem auto; display: block;">
  </div>

  <section id="acara" class="section" style="background: url('img/bg_wed1.jpg') no-repeat center center; background-size: cover; min-height: 100vh; display: flex; align-items: center; justify-content: center;">
    <div class="container">
      <div class="columns is-centered">
        <div class="column is-8">
          <div class="box has-text-centered fade-in" style="background-color: rgba(255, 255, 255, 0.514); backdrop-filter: blur(4px);">
            <h2 class="title is-5 fade-in" style="line-height: 1.8;">Q.S. Ar-Rum (21)<br>بِسْمِ اللهِ الرَّحْمنِ الرَّحِيمِ</h2>
            <p class="subtitle is-4 fade-in">وَمِنْ اٰيٰتِهٖٓ اَنْ خَلَقَ لَكُمْ مِّنْ اَنْفُسِكُمْ اَزْوَاجًا لِّتَسْكُنُوْٓا اِلَيْهَا وَجَعَلَ بَيْنَكُمْ مَّوَدَّةً وَّرَحْمَةًۗ اِنَّ فِيْ ذٰلِكَ لَاٰيٰتٍ لِّقَوْمٍ يَّتَفَكَّرُوْنَ ۝٢١</p>
            <p class="subtitle is-6 fade-in">Di antara tanda-tanda (kebesaran)-Nya ialah bahwa Dia menciptakan pasangan-pasangan untukmu dari (jenis) dirimu sendiri agar kamu merasa tenteram kepadanya. Dia menjadikan di antaramu rasa cinta dan kasih sayang. Sesungguhnya pada yang demikian itu benar-benar terdapat tanda-tanda (kebesaran Allah) bagi kaum yang berpikir.</p>
          </div>
        </div>
      </div>

      <br>

      <p class="title is-3 has-text-centered fade-in" style="font-family: 'Sacramento', cursive; font-weight: bold; font-size: 5rem; color: #aedcee; text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);">Acara</p>
      <div class="columns is-centered">
        <div class="column is-8">
          <div class="box has-text-centered fade-in" style="background-color: rgba(255, 255, 255, 0.514); backdrop-filter: blur(4px);">
            <h3 class="subtitle is-6 fade-in">Bahagia rasanya apabila anda berkenan hadir dan memberikan doa restu kepada kami. Kami mengundang anda untuk hadir dalam acara resepsi pernikahan kami berikut ini</h3>
            <p class="title is-5 fade-in" style="line-height: 3; font-family: 'Sacramento', cursive; font-size: 3rem;">Resepsi</p>
            <div class="columns is-vcentered">
              <div class="column is-half has-text-centered">
                <span class="icon is-large">
                  <i class="bi bi-clock" style="font-size: 3rem;"></i>
                </span>
                <p>05 Mei 2025 (11:00 - Selesai)</p>
                <a href="https://calendar.google.com/calendar/render?action=TEMPLATE&text=Ahro+%26+Ayfa+Wedding&dates=20250505T040000Z/20250505T090000Z&details=Resepsi+pernikahan+Ahro+%26+Ayfa&location=Grand+Jatra+Hotel+(Lt.5)&sf=true" target="_blank" class="button is-primary is-small mt-2">Tambahkan ke Kalendar</a>
              </div>
              <div class="column is-half has-text-centered">
                <span class="icon is-large">
                  <i class="bi bi-geo-alt" style="font-size: 3rem;"></i>
                </span>
                <p>Grand Jatra Hotel (Lt.5)</p>
                <a href="#info" class="button is-primary is-small mt-2">Lihat Peta</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <div class="divider">
    <img src="img/dividerblue.png" alt="Divider" class="animated-divider" style="width: 100%; margin: 0rem auto; display: block;">
  </div>

  <section id="home" class="hero is-fullheight" style="position: relative;">
    <div class="background" style="background-image: url('img/bg_wed1.jpg'); background-size: cover; background-position: center; background-repeat: no-repeat; position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: -1;"></div>
    <div class="hero-body">
      <div class="container has-text-centered fade-in">
        <div class="columns is-centered">
          <div class="column is-8">
            <h2 class="title fade-in" style="font-family: 'Sacramento', cursive; font-weight: bold; font-size: 3rem; color: #192022; text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);">Assalamualaikum Warahmatullahi Wabarakatuh</h2>
            <h3 class="subtitle fade-in">
              Dengan Rahmat Allah yang Maha Kuasa Insya Allah kami akan melangsungkan pernikahan pada keluarga:
            </h3>
          </div>
        </div>

        <!-- Couple Section -->
        <div class="columns is-vcentered is-multiline couple">
          <!-- Fahrozy Hidayat -->
          <div class="column is-half is-full-mobile has-text-centered fade-in">
            <figure class="image is-inline-block" style="width: 192px; height: 192px;">
              <img src="{{ asset('img/'.$pengantin->foto_pria) }}" alt="Fahrozy Hidayat" class="is-square" style="width: 192px; height: 192px; border-radius: 5%;">
            </figure>
            <h3 class="title mt-3 fade-in" style="font-family: 'Sacramento', cursive;">{{ $pengantin->nama_lengkap_pria }}</h3>
            <p>Putra dari Bapak {{ $pengantin->nama_ayah_pria }} & Ibu {{ $pengantin->nama_ibu_pria }}</p>
          </div>

          <!-- Khazel Hayfa -->
          <div class="column is-half is-full-mobile has-text-centered fade-in">
            <figure class="image is-inline-block" style="width: 192px; height: 192px;">
              <img src="{{ asset('img/'.$pengantin->foto_wanita) }}" alt="Khazel Hayfa" class="is-rounded" style="width: 192px; height: 192px; border-radius: 5%;">
            </figure>
            <h3 class="title mt-3 fade-in" style="font-family: 'Sacramento', cursive;">{{ $pengantin->nama_lengkap_wanita }}</h3>
            <p>Putri dari Bapak {{ $pengantin->nama_ayah_wanita }} & Ibu {{ $pengantin->nama_ibu_wanita }}</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <div class="divider">
    <img src="img/dividerblue.png" alt="Divider" class="animated-divider" style="width: 100%; margin: 0rem auto; display: block;">
  </div>

  <section id="info" class="section" style="background: url('img/bg_wed1.jpg') no-repeat center center; background-size: cover;">
    <div class="container has-text-centered"></div>
      <div class="columns is-centered">
        <div class="column is-8 is-full-mobile">
            <h2 class="title fade-in" style="font-family: 'Sacramento', cursive; font-weight: bold; font-size: 6rem; color: #192022; text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);">Lokasi</h2>
          <p class="alamat fade-in">
            Alamat: <br> {{ $pengantin->alamat_lokasi ?? 'Alamat belum diisi' }} <br>
            <br>
          </p>

@if(!empty($pengantin->embed_maps))
  {!! $pengantin->embed_maps !!}
@else
  <iframe 
    src="{{ $pengantin->link_maps ?? 'https://www.google.com/maps/embed?...' }}"
    width="100%" height="450" style="border:0;" allowfullscreen="" loading="lazy"></iframe>
@endif
  
@if(!empty($pengantin->link_maps))
  <a href="{{ $pengantin->link_maps }}" target="_blank" class="button is-light is-small my-3">
    Klik untuk membuka peta
  </a>
@endif
<br>
          <p class="description fade-in">
            Mohon untuk memastikan kembali tanggal dan lokasi acara.
Apabila setibanya di tempat tidak terdapat tanda-tanda berlangsungnya pernikahan, bisa jadi terjadi kekeliruan tanggal atau lokasi.
          </p>
        </div>
      </div>
    </div>
  </section>

  <div class="divider" style="z-index: 1; position: relative;">
    <img src="img/dividerblue.png" alt="Divider" class="animated-divider" style="width: 100%; margin: 0rem auto; display: block;">
  </div>

  <section id="story" class="section" style="position: relative; background-image: url('img/bg_wed1.jpg'); background-size: cover; background-position: center; background-repeat: no-repeat;">
  <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(255, 255, 255, 0.678); z-index: 1;"></div>
  <div class="container" style="position: relative; z-index: 2;">
    <div class="has-text-centered fade-in">
      <h2 class="title fade-in" style="font-family: 'Sacramento', cursive; font-weight: bold; font-size: 6rem; color: #192022; text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);">Story</h2>
      <p>Cinta ini tumbuh dari hal-hal kecil yang tak terduga, lalu perlahan menguat menjadi satu janji untuk bersama selamanya.</p>
    </div>

    <div class="columns">
      <div class="column">
        <ul class="timeline">
          @forelse($story as $i => $item)
            <li @if($i % 2 == 1) class="timeline-inverted" @endif>
              <div class="timeline-image fade-in"
                @if($item->gambar)
                  style="background-image: url('{{ asset('story/'.$item->gambar) }}');"
                @endif
              ></div>
              <div class="timeline-panel">
                <div class="timeline-heading">
                  <h3 class="title fade-in">{{ $item->judul }}</h3>
                  <span>{{ \Carbon\Carbon::parse($item->tanggal)->format('d F Y') }}</span>
                </div>
                <br>
                <div class="timeline-body fade-in">
                  <p>{{ $item->deskripsi }}</p>
                </div>
              </div>
            </li>
          @empty
            <li>
              <div class="timeline-panel">
                <div class="timeline-heading">
                  <h3 class="title fade-in">Belum ada story</h3>
                </div>
              </div>
            </li>
          @endforelse
        </ul>
      </div>
    </div>
  </div>
</section>

  <div class="divider">
    <img src="img/dividerblue.png" alt="Divider" class="animated-divider" style="width: 100%; margin: 0rem auto; display: block;">
  </div>

  <section id="gallery" class="section" style="position: relative; background-image: url('img/bg_wed1.jpg'); background-size: cover; background-position: center; background-repeat: no-repeat;">
    <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(255, 255, 255, 0.418); z-index: 1;"></div>
    <div class="container" style="position: relative; z-index: 2;">
      <div class="columns is-centered fade-in">
        <div class="column is-8 is-full-mobile has-text-centered fade-in">
          <h2 class="title fade-in" style="font-family: 'Sacramento', cursive; font-weight: bold; font-size: 6rem; color: #192022; text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);">Galeri</h2>
          <p>Kumpulan momen kebahagiaan kami<br>dari awal bertemu hingga menanti hari istimewa 💖</p>
        </div>
      </div>

      <div class="columns is-multiline is-centered">
  @forelse($galeri as $foto)
    <div class="column is-one-quarter-desktop is-one-third-tablet is-half-mobile has-text-centered fade-in" style="display: flex; flex-direction: column; align-items: center;">
      <a href="{{ asset('galeri/'.$foto->file) }}" class="glightbox" data-gallery="galeri" data-title="{{ $foto->caption ?? '' }}">
        <figure class="image" style="width: 100%; max-width: 320px;">
          <img src="{{ asset('galeri/'.$foto->file) }}" alt="{{ $foto->caption ?? 'Galeri' }}" style="width: 100%; height: 240px; object-fit: cover; border-radius: 10px; box-shadow: 0 2px 8px rgba(0,0,0,0.08);">
        </figure>
      </a>
      @if($foto->caption)
        <p class="has-text-centered mt-2">{{ $foto->caption }}</p>
      @endif
    </div>
  @empty
    <div class="column has-text-centered">Belum ada foto galeri.</div>
  @endforelse
</div>
    </div>
  </section>
  
  <div class="divider">
    <img src="img/dividerblue.png" alt="Divider" class="animated-divider" style="width: 100%; margin: 0rem auto; display: block;">
  </div>

  <section id="rsvp" class="section" style="position: relative; background-image: url('img/bg_wed1.jpg'); background-size: cover; background-position: center; background-repeat: no-repeat;">
    <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(255, 255, 255, 0); z-index: 1;"></div>
    <div class="container" style="position: relative; z-index: 2;">
        @if (session('success'))
        <div class="notification is-success mb-5">{{ session('success') }}</div>
        @elseif (session('error'))
        <div class="notification is-danger mb-5">{{ session('error') }}</div>
        @endif
        
        <div class="columns is-centered fade-in">
            <div class="column is-8 has-text-centered">
                <h2 class="title" style="font-family: 'Sacramento', cursive; font-weight: bold; font-size: 6rem; color: #192022; text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);">RSVP</h2>
                <p>Isi form di bawah ini untuk melakukan konfirmasi kehadiran.</p>
            </div>
        </div>
        
        <form class="columns is-multiline is-centered" method="POST" action="{{ url('/proses_rsvp') }}">
    @csrf
    <input type="hidden" name="from" value="index">
    <div class="column is-12">
        <div class="field">
            <label class="label" for="nama">Nama</label>
            <div class="control">
                <input class="input" type="text" id="nama" name="nama" placeholder="Masukkan nama Anda" required>
            </div>
        </div>
    </div>
    <div class="column is-12">
        <div class="field">
            <label class="label" for="jumlah">Jumlah Orang</label>
            <div class="control">
                <input class="input" type="number" id="jumlah" name="jumlah" placeholder="Masukkan jumlah tamu" min="1" max="10" required>
            </div>
        </div>
    </div>
    <div class="column is-12">
        <div class="field">
            <label class="label" for="konfirm">Konfirmasi Kehadiran</label>
            <div class="control">
                <div class="select">
                    <select name="konfirm" id="konfirm" required>
                        <option value="" disabled selected>Pilih salah satu</option>
                        <option value="1">Hadir</option>
                        <option value="0">Tidak Hadir</option>
                    </select>
                </div>
            </div>
        </div>
    </div>
    <div class="column is-12" style="margin-top: 35px;">
        <div class="control">
            <button class="button is-primary" type="submit">Kirim</button>
        </div>
    </div>
</form>
    </div>
</section>

  <div class="divider">
    <img src="img/dividerblue.png" alt="Divider" class="animated-divider" style="width: 100%; margin: 0rem auto; display: block;">
  </div>
  
  <section id="wishes" class="section" style="position: relative; background-image: url('img/bg_wed1.jpg'); background-size: cover; background-position: center; background-repeat: no-repeat;">
    <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(255, 255, 255, 0); z-index: 1;"></div>
    <div class="container" style="position: relative; z-index: 2;">
      <div class="columns is-centered fade-in">
        <div class="column is-8 has-text-centered">
          <h2 class="title" style="font-family: 'Sacramento', cursive; font-weight: bold; font-size: 6rem; color: #192022; text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);">Doa & Ucapan</h2>
          <p>Berikan doa dan ucapan terbaik Anda untuk kami.</p>
        </div>
      </div>
      <form class="columns is-multiline is-centered" method="POST" id="wishesForm" action="{{ url('/wishes') }}">
  @csrf
  <div class="column is-12">
    <div class="field">
      <label class="label" for="wishName">Nama</label>
      <div class="control">
        <input class="input" type="text" id="wishName" name="nama" placeholder="Masukkan nama Anda" required>
      </div>
    </div>
  </div>
  <div class="column is-12">
    <div class="field">
      <label class="label" for="wishMessage">Pesan</label>
      <div class="control">
        <textarea class="textarea" id="wishMessage" name="ucapan" placeholder="Masukkan pesan Anda" required></textarea>
      </div>
    </div>
  </div>
  <div class="column is-12" style="margin-top: 35px;">
    <div class="control">
      <button class="button is-primary" type="submit">Kirim</button>
    </div>
  </div>
</form>
      <div class="columns is-multiline is-centered mt-5" id="wishesContainer">
  @forelse($wishes as $wish)
    <div class="column is-one-third">
      <div class="box">
        <p><strong>{{ $wish->nama }}</strong></p>
        <p>{{ $wish->ucapan }}</p>
      </div>
    </div>
  @empty
    <div class="column has-text-centered">Belum ada doa dan ucapan.</div>
  @endforelse
</div>
    </div>
  </section>

  <div class="divider">
    <img src="img/dividerblue.png" alt="Divider" class="animated-divider" style="width: 100%; margin: 0rem auto; display: block;">
  </div>

<section id="gifts" class="section" style="background: #f7fafc;">
  <div class="container has-text-centered fade-in">
    <h2 class="title" style="font-family: 'Sacramento', cursive; font-size: 4rem;">Hadiah</h2>
    <p>Doa restu Anda sudah sangat berarti. Namun jika ingin mengirim hadiah, berikut info rekening/QRIS kami:</p>
    @if($hadiah)
      <div class="box" style="max-width:400px; margin:2rem auto;">
        <p><b>{{ $hadiah->nama_bank ?? '-' }}</b></p>
        <p>a.n. <b>{{ $hadiah->nama_pemilik ?? '-' }}</b></p>
        <p>No. Rekening: <b>{{ $hadiah->no_rekening ?? '-' }}</b></p>
        @if($hadiah->qris)
          <img src="{{ asset('img/'.$hadiah->qris) }}" alt="QRIS" style="max-width:200px; margin:1rem auto;">
        @endif
        @if($hadiah->catatan)
          <p class="mt-3">{{ $hadiah->catatan }}</p>
        @endif
      </div>
    @else
      <div class="box" style="max-width:400px; margin:2rem auto;">
        <p>Belum ada data hadiah.</p>
      </div>
    @endif
  </div>
</section>

  <div class="divider">
    <img src="img/dividerblue.png" alt="Divider" class="animated-divider" style="width: 100%; margin: 0rem auto; display: block;">
  </div>

  <section id="live" class="section" style="background: #f7fafc;">
  <div class="container has-text-centered fade-in">
    <h2 class="title" style="font-family: 'Sacramento', cursive; font-size: 4rem;">Live Streaming</h2>
    @if($live && ($live->embed || $live->link))
      <div class="box" style="max-width:600px; margin:2rem auto;">
        <p><b>{{ $live->judul ?? 'Live Acara' }}</b></p>
        @if($live->embed)
          {!! $live->embed !!}
        @elseif($live->link)
          <a href="{{ $live->link }}" target="_blank" class="button is-primary">Tonton Live</a>
        @endif
      </div>
    @else
      <div class="box" style="max-width:400px; margin:2rem auto;">
        <p>Belum ada info live streaming.</p>
      </div>
    @endif
  </div>
</section>

<footer class="footer">
  <div class="container">
    <div class="columns is-centered">
      <div class="column is-narrow">
        <small class="has-text-centered block">&copy;2025 Ahro & Ayfa Wedding. Website dibuat oleh Kelompok 7.</small>


        <div class="mt-3 has-text-centered">
          <a href="#" class="mr-2"><span class="icon"><i class="bi bi-instagram"></i></span></a>
          <a href="#" class="mr-2"><span class="icon"><i class="bi bi-youtube"></i></span></a>
          <a href="#" class="mr-2"><span class="icon"><i class="bi bi-twitter"></i></span></a>
          <a href="#" class="mr-2"><span class="icon"><i class="bi bi-facebook"></i></span></a>
          <a href="#" class="mr-2"><span class="icon"><i class="bi bi-tiktok"></i></span></a>
        </div>

        <div class="mt-3 has-text-centered">
          <a href="{{ url('dashboard') }}" class="button is-primary is-small">Go to Dashboard</a>

      </div>
    </div>
  </div>
</footer>

<div id="audio-container">
  <audio id="song" loop>
    <source src="audio/sezairi.mp3" type="audio/mp3">
  </audio>

  <div class="audio-icon-wrapper" style="display: none;">
    <span class="icon"><i class="bi bi-disc"></i></span>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bulma@0.9.4/dist/js/bulma.min.js"></script>

<script src="https://cdn.jsdelivr.net/npm/glightbox/dist/js/glightbox.min.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function () {
    const lightbox = GLightbox({
      selector: '.glightbox'
    });
  });
</script>

<script>
  simplyCountdown('.simply-countdown', {
    year: 2025, // required
    month: 10, // required
    day: 5, // required
    hours: 11, // Default is 0 [0-23] integer
    words: { //words displayed into the countdown
      days: { singular: 'hari', plural: 'hari' },
      hours: { singular: 'jam', plural: 'jam' },
      minutes: { singular: 'menit', plural: 'menit' },
      seconds: { singular: 'detik', plural: 'detik' }
    },
  });
</script>

<script>
  const stickyTop = document.querySelector('.is-sticky');
  const offcanvas = document.querySelector('.offcanvas');

  offcanvas.addEventListener('show.bs.offcanvas', function () {
    stickyTop.style.overflow = 'visible';
  });

  offcanvas.addEventListener('hidden.bs.offcanvas', function () {
    stickyTop.style.overflow = 'hidden';
  });
</script>

<script>
  const rootElement = document.querySelector(":root");
  const audioIconWrapper = document.querySelector('.audio-icon-wrapper');
  const audioIcon = document.querySelector('.audio-icon-wrapper i');
  const song = document.querySelector('#song');
  let isPlaying = false;

  function disableScroll() {
    scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

    window.onscroll = function () {
      window.scrollTo(scrollTop, scrollLeft);
    }

    rootElement.style.scrollBehavior = 'auto';
  }

  function enableScroll() {
    window.onscroll = function () { }
    rootElement.style.scrollBehavior = 'smooth';
    // localStorage.setItem('opened', 'true');
    playAudio();
  }

  function playAudio() {
    song.volume = 0.1;
    audioIconWrapper.style.display = 'flex';
    song.play();
    isPlaying = true;
  }

  audioIconWrapper.onclick = function () {
    if (isPlaying) {
      song.pause();
      audioIcon.classList.remove('bi-disc');
      audioIcon.classList.add('bi-pause-circle');
    } else {
      song.play();
      audioIcon.classList.add('bi-disc');
      audioIcon.classList.remove('bi-pause-circle');
    }

    isPlaying = !isPlaying;
  }

  // if (!localStorage.getItem('opened')) {
  //   disableScroll();
  // }
  disableScroll();
</script>

<script>
  document.addEventListener('DOMContentLoaded', function () {
  const fadeInElements = document.querySelectorAll('.fade-in');

  const observer = new IntersectionObserver((entries, observer) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('visible');
        observer.unobserve(entry.target);
      }
    });
  }, {
    threshold: 0.1
  });

  fadeInElements.forEach(element => {
    observer.observe(element);
  });
});
</script>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const burger = document.querySelector('.navbar-burger');
    const menu = document.querySelector('.navbar-menu');

    // Add click event listener to the burger icon
    burger.addEventListener('click', () => {
      // Toggle the "is-active" class on both the burger and the menu
      burger.classList.toggle('is-active');
      menu.classList.toggle('is-active');
    });
  });
</script>

<script>
  const urlParams = new URLSearchParams(window.location.search);
  const nama = urlParams.get('n') || '';
  const pronoun = urlParams.get('p') || 'Bapak/Ibu/Saudara/i';
  const namaContainer = document.querySelector('.hero h4 span');
  namaContainer.innerText = `${pronoun} ${nama},`.replace(/ ,$/, ',');

  document.querySelector('#nama').value = nama;
</script>

<script>
document.addEventListener('DOMContentLoaded', function () {
    const rsvpForm = document.querySelector('form[action="{{ url('/proses_rsvp') }}"]');
    if (rsvpForm) {
        rsvpForm.addEventListener('submit', function (e) {
            e.preventDefault();
            const data = new FormData(rsvpForm);

            fetch(rsvpForm.action, {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('input[name="_token"]').value
                },
                body: data
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Berhasil!',
                        text: result.message,
                        confirmButtonText: 'OK'
                    });
                    rsvpForm.reset();
                } else {
                    Swal.fire('Gagal', result.message || 'Terjadi kesalahan.', 'error');
                }
            })
            .catch(() => {
                Swal.fire('Gagal', 'Terjadi kesalahan. Silakan coba lagi.', 'error');
            });
        });
    }
});
</script>

</body>

</html>