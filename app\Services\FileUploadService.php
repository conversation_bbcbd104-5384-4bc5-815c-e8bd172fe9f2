<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Facades\Image;

class FileUploadService
{
    /**
     * Upload and resize image file
     *
     * @param UploadedFile $file
     * @param string $directory
     * @param int $maxWidth
     * @param int $maxHeight
     * @return string
     */
    public function uploadImage(UploadedFile $file, string $directory, int $maxWidth = 800, int $maxHeight = 600): string
    {
        $filename = time() . '_' . $file->getClientOriginalName();
        $path = public_path($directory);

        // Create directory if it doesn't exist
        if (!file_exists($path)) {
            mkdir($path, 0755, true);
        }

        // Resize and save image
        $image = Image::make($file);
        $image->resize($maxWidth, $maxHeight, function ($constraint) {
            $constraint->aspectRatio();
            $constraint->upsize();
        });

        $image->save($path . '/' . $filename, 80);

        return $filename;
    }

    /**
     * Upload file without processing
     *
     * @param UploadedFile $file
     * @param string $directory
     * @return string
     */
    public function uploadFile(UploadedFile $file, string $directory): string
    {
        $filename = time() . '_' . $file->getClientOriginalName();
        $file->move(public_path($directory), $filename);

        return $filename;
    }

    /**
     * Delete file from public directory
     *
     * @param string $filename
     * @param string $directory
     * @return bool
     */
    public function deleteFile(string $filename, string $directory): bool
    {
        $filePath = public_path($directory . '/' . $filename);
        
        if (file_exists($filePath)) {
            return unlink($filePath);
        }

        return false;
    }

    /**
     * Validate image file
     *
     * @param UploadedFile $file
     * @param int $maxSize (in KB)
     * @return array
     */
    public function validateImage(UploadedFile $file, int $maxSize = 2048): array
    {
        $errors = [];

        // Check file size
        if ($file->getSize() > $maxSize * 1024) {
            $errors[] = "File size must be less than {$maxSize}KB";
        }

        // Check file type
        $allowedTypes = ['jpg', 'jpeg', 'png', 'gif'];
        $extension = strtolower($file->getClientOriginalExtension());
        
        if (!in_array($extension, $allowedTypes)) {
            $errors[] = 'File must be an image (jpg, jpeg, png, gif)';
        }

        return $errors;
    }
}
