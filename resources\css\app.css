:root {
  --pink: #aedcee;
  --bg: #0a0a0a;
  --shadow: 0 4px 4px rgb(0 0 0 / 0.5);
}

body {
  font-size: 1.2rem;
  font-family: 'Work Sans', sans-serif;
  min-height: 4000px;
}

/* Ensure all sections are centered */
.section {
  text-align: center;
}

/* Center columns inside the container */
.columns.is-centered {
  justify-content: center;
}

/* Ensure iframe (Google Maps) is centered */
iframe {
  display: block;
  margin: 0 auto;
}

.video-container {
  position: relative;
  width: 100%;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  height: 0;
  overflow: hidden;
  max-width: 100%;
}

.video-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
}

@media (max-width: 768px) {
  .column {
    text-align: center;
  }

  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #fce4cc;
  z-index: -1;
}

.hero {
  position: relative;
  min-height: 100vh;
}

.hero h1,
.hero h4,
.hero p {
  text-shadow: var(--shadow);
}

.hero h1 {
  font-family: 'Sacramento', cursive;
  font-size: 6rem;
}

.hero h4 {
  font-size: 1.6rem;
}

.hero p {
  font-size: 1.4rem;
}

.hero a {
  color: var(--pink);
  background-color: white;
  box-shadow: var(--shadow);
}

.hero a:hover {
  background-color: var(--pink);
  color: white;
}

.fade-in {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 2s ease, transform 2s ease; 
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

.animated-divider {
  animation: bounce-z 3s infinite;
  max-width: 100%; /* Ensure it doesn't exceed the viewport width */
  margin: 0 auto; /* Center the divider */
}

@keyframes bounce-z {
  0%, 100% {
    transform: scale(1); /* No size change */
  }
  50% {
    transform: scale(1.05); /* Slightly enlarge without affecting position */
  }
}

html, body {
  overflow-x: hidden; /* Prevent horizontal scrolling */
}

.navbar {
  background-color: rgba(255, 255, 255, 0.514) !important;
  backdrop-filter: blur(8px);
  font-size: 1.2rem; /* Increase font size */
  height: 4rem; /* Adjust height */
}

.mynavbar .offcanvas {
  height: 100vh;
}

.mynavbar .navbar-brand,
.mynavbar .offcanvas-title {
  font-family: 'Sacramento', cursive;
  font-size: 3.2rem;
  font-weight: bold;
}

.mynavbar .nav-link {
  text-transform: uppercase;
}

.home {
  background-image: url(img/bg.png);
  background-size: cover;
  min-height: 100vh;
  margin-top: -6rem;
  padding-top: 15rem;
  padding-bottom: 5rem;
}

.home h2,
.info h2,
.story h2,
.gallery h2,
.rsvp h2,
.gifts h2 {
  color: var(--pink);
  font-family: 'Sacramento';
  font-size: 5rem;
  font-weight: bold;
}

.home h3 {
  color: #444;
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.home p {
  font-size: 1.1rem;
  color: #666;
}

.home .couple {
  margin-top: 100px;
}

.home .couple h3 {
  font-family: 'Sacramento';
  font-size: 2.4rem;
  color: var(--pink);
}

.home .couple img {
  width: 100%;
}

.home .heart {
  width: 50px;
  height: 50px;
  background-color: white;
  display: flex;
  border-radius: 50%;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
  position: absolute;
  left: 50%;
  transform: translateX(-50%) translateY(65px);
}

.home .heart i {
  margin: auto;
  color: var(--pink);
}

.info {
  background-color: var(--bg);
  color: white;
  padding-top: 10rem;
  padding-bottom: 8rem;
}

.info .alamat {
  font-size: 1.1rem;
}

.info .description {
  font-size: 1rem;
  font-weight: 300;
}

.info .card {
  background-color: rgba(255, 255, 255, 0.2) !important;
  color: white !important;
  border: 1px solid #999;
}

.info .card-header {
  border-bottom: 1px solid #999;
  text-transform: uppercase;
  letter-spacing: 0.1rem;
}

.info .card-body {
  font-size: 1rem;
}

.info .card-footer {
  border-top: 1px solid #999;
  font-size: 0.9rem;
  font-weight: 300;
}

.story,
.gallery,
.rsvp,
.gifts {
  padding-top: 10rem;
  padding-bottom: 8rem;
}

.story span,
.gallery span,
.gifts span {
  text-transform: uppercase;
  color: #666;
  font-size: 0.9rem;
  letter-spacing: 1px;
  display: block;
  margin-bottom: 1rem;
}

.story p,
.gallery p,
.rsvp p,
.gifts p {
  font-size: 1rem;
  font-weight: 300;
}

.timeline {
  list-style: none;
  padding: 1.4rem 0;
  margin-top: 1rem;
  position: relative;
}

.timeline::before {
  content: '';
  top: 0;
  bottom: 0;
  position: absolute;
  width: 1px;
  background-color: #ccc;
  left: 50%;
}

.timeline li {
  margin-bottom: 1.5rem;
  position: relative;
}

.timeline li::before,
.timeline li::after {
  content: '';
  display: table;
}

.timeline li::after {
  clear: both;
}

.timeline li .timeline-image {
  width: 160px;
  height: 160px;
  background-color: #ccc;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  border-radius: 50%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.timeline li .timeline-panel {
  width: 40%;
  float: left;
  border: 1px solid #ccc;
  padding: 2rem;
  position: relative;
  border-radius: 8px;
  background-color: #fff;
}

.timeline li .timeline-panel::before {
  content: '';
  display: inline-block;
  position: absolute;
  border-top: 15px solid transparent;
  border-left: 15px solid #ccc;
  border-right: 0 solid #ccc;
  border-bottom: 15px solid transparent;
  top: 80px;
  right: -15px;
}

.timeline li .timeline-panel::after {
  content: '';
  position: absolute;
  display: inline-block;
  border-top: 14px solid transparent;
  border-left: 14px solid #fff;
  border-right: 0 solid #fff;
  border-bottom: 14px solid transparent;
  top: 81px;
  right: -13px;
}

.timeline li.timeline-inverted .timeline-panel {
  float: right;
}

.timeline li.timeline-inverted .timeline-panel::before {
  border-left-width: 0;
  border-right-width: 15px;
  left: -15px;
  right: auto;
}

.timeline li.timeline-inverted .timeline-panel::after {
  border-left-width: 0;
  border-right-width: 14px;
  left: -13px;
  right: auto;
}

.gallery {
  background-color: #f5f5f5;
}

.rsvp {
  background-color: var(--bg);
}

.rsvp h2 {
  font-size: 4.5rem;
}

.rsvp p {
  color: white;
}

.rsvp form label {
  color: white;
}

.rsvp button {
  background-color: var(--pink);
  color: white;
  border: 1px solid var(--pink);
}

.rsvp button:hover {
  background-color: white;
  color: var(--pink);
  border: 1px solid white;
}

footer {
  padding: 3rem;
  background-color: rgb(181, 243, 243);
  color: rgb(0, 6, 7);
  margin-bottom: 0
}

footer a {
  color: var(--bg);
  font-weight: bold;
  text-decoration: none;
}

footer a:hover {
  color: rgb(48, 211, 233);
}

footer li {
  list-style: none;
  display: inline;
  margin: 0.5rem;
}

.audio-icon-wrapper {
  z-index: 9999;
  width: 4rem;
  height: 4rem;
  font-size: 4rem;
  position: fixed;
  bottom: 2.5rem;
  right: 2rem;
  cursor: pointer;
  color: white;
  opacity: 0.5;
  mix-blend-mode: difference;
  animation: rotating 4s linear infinite;
  transform-origin: center;
  display: flex;
  justify-content: center;
  align-items: center;
  line-height: 0;
}

@keyframes rotating {
  from {
    transform: rotate(0);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Media Query */
/* Extra large */
@media (max-width: 1200px) {
  .home .heart {
    transform: translateX(-50%) translateY(45px);
  }
}

/* laptop */
@media (max-width: 992px) {
  html {
    font-size: 75%;
  }

  .simply-countdown > .simply-section {
    padding: 40px;
  }

  .home .heart {
    display: none;
  }

  .timeline::before {
    left: 60px;
  }

  .timeline li .timeline-image {
    left: 15px;
    margin-left: 45px;
    top: 16px;
  }

  .timeline li .timeline-panel {
    width: calc((100% - 200px));
    float: right;
  }

  .timeline li .timeline-panel::before {
    border-left-width: 0;
    border-right-width: 15px;
    left: -15px;
    right: auto;
  }

  .timeline li .timeline-panel::after {
    border-left-width: 0;
    border-right-width: 14px;
    left: -13px;
    right: auto;
  }
}

/* tablet */
@media (max-width: 768px) {
  html {
    font-size: 65%;
  }

  .simply-countdown > .simply-section {
    padding: 40px;
    margin: 5px;
  }

  .mynavbar .nav-link {
    font-size: 2rem;
    text-align: center;
  }

  .sticky-top {
    overflow: hidden;
  }

  .timeline li .timeline-image {
    width: 140px;
    height: 140px;
  }
}

/* mobile phone */
@media (max-width: 576px) {
  html {
    font-size: 60%;
  }

  .simply-countdown > .simply-section {
    padding: 24px;
    margin: 3px;
  }

  .timeline li .timeline-image {
    width: 60px;
    height: 60px;
    transform: translateX(-50px);
  }

  .timeline li .timeline-panel {
    width: 65%;
    transform: translateX(-5px);
  }

  .timeline li .timeline-panel::before {
    top: 30px;
  }

  .timeline li .timeline-panel::after {
    top: 31px;
  }

  .timeline::before {
    transform: translateX(-20px);
  }
}
