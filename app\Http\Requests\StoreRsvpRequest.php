<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreRsvpRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'nama' => 'required|string|max:255',
            'jumlah' => 'required|integer|min:1|max:10',
            'konfirm' => 'required|in:0,1',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'nama.required' => 'Nama wajib diisi.',
            'nama.max' => 'Nama maksimal 255 karakter.',
            'jumlah.required' => 'Jumlah tamu wajib diisi.',
            'jumlah.integer' => 'Jumlah tamu harus berupa angka.',
            'jumlah.min' => 'Jumlah tamu minimal 1 orang.',
            'jumlah.max' => 'Jumlah tamu maksimal 10 orang.',
            'konfirm.required' => 'Konfirmasi kehadiran wajib dipilih.',
            'konfirm.in' => 'Konfirmasi kehadiran tidak valid.',
        ];
    }
}
