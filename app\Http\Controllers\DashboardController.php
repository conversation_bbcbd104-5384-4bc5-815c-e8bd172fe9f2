<?php

namespace App\Http\Controllers;

use App\Models\Rsvp;
use App\Models\Wish;
use App\Models\Pengantin;
use App\Models\Galeri;
use App\Models\Story;
use App\Models\Hadiah;
use App\Models\Live;
use App\Services\FileUploadService;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    protected $fileUploadService;

    public function __construct(FileUploadService $fileUploadService)
    {
        $this->middleware('admin.auth');
        $this->fileUploadService = $fileUploadService;
    }

    /**
     * Display the dashboard with all statistics
     */
    public function index()
    {
        $rsvps = Rsvp::all();
        $wishes = Wish::latest()->get();
        $pengantin = Pengantin::first();
        $galeri = Galeri::latest()->get();
        $story = Story::orderBy('tanggal')->get();
        $hadiah = Hadiah::first();
        $live = Live::first();

        // Calculate statistics
        $rsvpCount = $rsvps->count();
        $totalHadir = $rsvps->where('konfirm', true)->count();
        $totalTidakHadir = $rsvps->where('konfirm', false)->count();
        $jumlahOrangHadir = $rsvps->where('konfirm', true)->sum('jumlah');
        $jumlahOrangTidakHadir = $rsvps->where('konfirm', false)->sum('jumlah');
        $wishesCount = $wishes->count();
        $galeriCount = $galeri->count();

        return view('dashboard', compact(
            'rsvps', 'wishes', 'pengantin', 'galeri', 'story', 'hadiah', 'live',
            'rsvpCount', 'totalHadir', 'totalTidakHadir',
            'jumlahOrangHadir', 'jumlahOrangTidakHadir',
            'wishesCount', 'galeriCount'
        ));
    }

    /**
     * Update pengantin data
     */
    public function updatePengantin(Request $request)
    {
        $request->validate([
            'nama_pria' => 'nullable|string|max:255',
            'nama_lengkap_pria' => 'nullable|string|max:255',
            'ortu_pria' => 'nullable|string|max:255',
            'nama_ayah_pria' => 'nullable|string|max:255',
            'nama_ibu_pria' => 'nullable|string|max:255',
            'foto_pria' => 'nullable|image|mimes:jpg,jpeg,png|max:2048',
            'nama_wanita' => 'nullable|string|max:255',
            'nama_lengkap_wanita' => 'nullable|string|max:255',
            'ortu_wanita' => 'nullable|string|max:255',
            'nama_ayah_wanita' => 'nullable|string|max:255',
            'nama_ibu_wanita' => 'nullable|string|max:255',
            'foto_wanita' => 'nullable|image|mimes:jpg,jpeg,png|max:2048',
        ]);

        $data = $request->only([
            'nama_pria', 'nama_lengkap_pria', 'ortu_pria', 'nama_ayah_pria', 'nama_ibu_pria',
            'nama_wanita', 'nama_lengkap_wanita', 'ortu_wanita', 'nama_ayah_wanita', 'nama_ibu_wanita'
        ]);

        $pengantin = Pengantin::first();

        // Upload foto pria
        if ($request->hasFile('foto_pria')) {
            if ($pengantin && $pengantin->foto_pria) {
                $this->fileUploadService->deleteFile($pengantin->foto_pria, 'img');
            }
            $data['foto_pria'] = $this->fileUploadService->uploadImage($request->file('foto_pria'), 'img');
        }

        // Upload foto wanita
        if ($request->hasFile('foto_wanita')) {
            if ($pengantin && $pengantin->foto_wanita) {
                $this->fileUploadService->deleteFile($pengantin->foto_wanita, 'img');
            }
            $data['foto_wanita'] = $this->fileUploadService->uploadImage($request->file('foto_wanita'), 'img');
        }

        Pengantin::updateOrCreate(['id' => 1], $data);

        return redirect()->route('dashboard')->with('success', 'Data pengantin berhasil diupdate!');
    }

    /**
     * Update lokasi data
     */
    public function updateLokasi(Request $request)
    {
        $request->validate([
            'alamat_lokasi' => 'nullable|string',
            'link_maps' => 'nullable|url',
            'embed_maps' => 'nullable|string',
        ]);

        $data = $request->only(['alamat_lokasi', 'link_maps', 'embed_maps']);
        Pengantin::updateOrCreate(['id' => 1], $data);

        return redirect()->route('dashboard')->with('success', 'Lokasi berhasil diupdate!');
    }

    /**
     * Upload galeri photo
     */
    public function uploadGaleri(Request $request)
    {
        $request->validate([
            'file' => 'required|image|mimes:jpg,jpeg,png|max:2048',
            'caption' => 'nullable|string|max:255'
        ]);

        $filename = $this->fileUploadService->uploadImage($request->file('file'), 'galeri');

        Galeri::create([
            'file' => $filename,
            'caption' => $request->caption
        ]);

        return redirect()->route('dashboard')->with('success', 'Foto berhasil diupload!');
    }

    /**
     * Delete galeri photo
     */
    public function deleteGaleri($id)
    {
        $galeri = Galeri::findOrFail($id);

        $this->fileUploadService->deleteFile($galeri->file, 'galeri');
        $galeri->delete();

        return redirect()->route('dashboard')->with('success', 'Foto berhasil dihapus!');
    }

    /**
     * Store new story
     */
    public function storeStory(Request $request)
    {
        $request->validate([
            'judul' => 'required|string|max:255',
            'tanggal' => 'required|date',
            'deskripsi' => 'required|string',
            'gambar' => 'nullable|image|mimes:jpg,jpeg,png|max:2048'
        ]);

        $data = $request->only(['judul', 'tanggal', 'deskripsi']);

        if ($request->hasFile('gambar')) {
            $data['gambar'] = $this->fileUploadService->uploadImage($request->file('gambar'), 'story');
        }

        Story::create($data);

        return redirect()->route('dashboard')->with('success', 'Story berhasil ditambahkan!');
    }

    /**
     * Delete story
     */
    public function deleteStory($id)
    {
        $story = Story::findOrFail($id);

        if ($story->gambar) {
            $this->fileUploadService->deleteFile($story->gambar, 'story');
        }

        $story->delete();

        return redirect()->route('dashboard')->with('success', 'Story berhasil dihapus!');
    }

    /**
     * Update hadiah information
     */
    public function updateHadiah(Request $request)
    {
        $request->validate([
            'nama_bank' => 'nullable|string|max:255',
            'nama_pemilik' => 'nullable|string|max:255',
            'no_rekening' => 'nullable|string|max:255',
            'catatan' => 'nullable|string',
            'qris' => 'nullable|image|mimes:jpg,jpeg,png|max:2048'
        ]);

        $data = $request->only(['nama_bank', 'nama_pemilik', 'no_rekening', 'catatan']);

        if ($request->hasFile('qris')) {
            $hadiah = Hadiah::first();
            if ($hadiah && $hadiah->qris) {
                $this->fileUploadService->deleteFile($hadiah->qris, 'img');
            }
            $data['qris'] = $this->fileUploadService->uploadImage($request->file('qris'), 'img');
        }

        Hadiah::updateOrCreate(['id' => 1], $data);

        return redirect()->route('dashboard')->with('success', 'Info hadiah berhasil diupdate!');
    }

    /**
     * Update live streaming information
     */
    public function updateLive(Request $request)
    {
        $request->validate([
            'judul' => 'nullable|string|max:255',
            'link' => 'nullable|url',
            'embed' => 'nullable|string'
        ]);

        $data = $request->only(['judul', 'link', 'embed']);
        Live::updateOrCreate(['id' => 1], $data);

        return redirect()->route('dashboard')->with('success', 'Info live berhasil diupdate!');
    }
}

