<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreWishRequest;
use App\Models\Wish;
use Illuminate\Http\Request;

class WishController extends Controller
{
    /**
     * Display a listing of wishes
     */
    public function index()
    {
        $wishes = Wish::latest()->get();
        return view('dashboard.wishes.index', compact('wishes'));
    }

    /**
     * Store a newly created wish from dashboard
     */
    public function store(StoreWishRequest $request)
    {
        Wish::create($request->validated());

        return redirect()->route('dashboard')->with('success', 'Ucapan berhasil ditambahkan!');
    }

    /**
     * Store a newly created wish from public page
     */
    public function storeFromIndex(StoreWishRequest $request)
    {
        Wish::create($request->validated());

        return redirect('/#wishes')->with('success', 'Doa dan ucapan berhasil terkirim!');
    }

    /**
     * Remove the specified wish
     */
    public function destroy($id)
    {
        $wish = Wish::findOrFail($id);
        $wish->delete();

        return redirect()->route('dashboard')->with('success', 'Ucapan berhasil dihapus!');
    }
}
