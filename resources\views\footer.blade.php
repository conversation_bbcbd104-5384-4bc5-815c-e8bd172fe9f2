@include('header')
<script>
    // Initialize DataTable if the element exists
    if (document.querySelector('#example')) {
        new DataTable('#example');
    }

    function deleteBoxSwet(url) {
        Swal.fire({
            title: '<PERSON><PERSON><PERSON>h kamu yakin?',
            text: "Data akan dihapus!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            cancelButtonText: 'Jangan dong!',
            confirmButtonText: 'Ya, hapus aja!'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = url;
            }
        });
    }
</script>