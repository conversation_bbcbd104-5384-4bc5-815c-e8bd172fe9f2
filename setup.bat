@echo off
echo ========================================
echo Wedding Invitation System Setup
echo ========================================
echo.

echo Installing Composer dependencies...
composer install
if %errorlevel% neq 0 (
    echo Error: Failed to install Composer dependencies
    pause
    exit /b 1
)

echo.
echo Installing NPM dependencies...
npm install
if %errorlevel% neq 0 (
    echo Error: Failed to install NPM dependencies
    pause
    exit /b 1
)

echo.
echo Copying environment file...
if not exist .env (
    copy .env.example .env
    echo Environment file created. Please configure your database settings in .env
) else (
    echo Environment file already exists.
)

echo.
echo Generating application key...
php artisan key:generate

echo.
echo Running database migrations...
php artisan migrate
if %errorlevel% neq 0 (
    echo Error: Failed to run migrations. Please check your database configuration.
    pause
    exit /b 1
)

echo.
echo Seeding database with initial data...
php artisan db:seed

echo.
echo Creating storage link...
php artisan storage:link

echo.
echo Creating required directories...
if not exist "public\img" mkdir "public\img"
if not exist "public\galeri" mkdir "public\galeri"
if not exist "public\story" mkdir "public\story"

echo.
echo ========================================
echo Setup completed successfully!
echo ========================================
echo.
echo Default admin credentials:
echo Email: <EMAIL>
echo Password: password123
echo.
echo You can now run: php artisan serve
echo Then visit: http://localhost:8000
echo.
pause
