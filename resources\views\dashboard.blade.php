@include('header')
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Dashboard - Kelompok 7</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Sacramento&family=Work+Sans:wght@100;300;400;600;700&display=swap" rel="stylesheet">
  <link rel="icon" type="image/png" href="{{ asset('favicon.png') }}">
  <style>
    .navbar {
      background-color: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(4px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .navbar-brand a {
      font-family: 'Sacramento', cursive;
      font-size: 2rem;
      font-weight: bold;
      color: #333;
      text-decoration: none !important;
    }
    .navbar-brand a:hover {
      color: #52b2b9;
    }
    .navbar-menu .navbar-item {
      font-family: 'Work Sans', sans-serif;
      font-weight: 600;
      color: #333;
    }
    .navbar-menu .navbar-item:hover {
      color: #52b2b9;
    }
    body {
      font-family: 'Work Sans', sans-serif;
      margin: 0;
      padding: 0;
    }
    .content {
      padding: 2rem;
    }
    .login-modal {
      display: flex;
      justify-content: center;
      align-items: center;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      z-index: 1000;
    }
    .login-modal .box {
      width: 100%;
      max-width: 400px;
    }

    .image.is-32x32 img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}
    .navbar-link {
  min-height: 40px;
}
.navbar-link .icon {
  display: flex;
  align-items: center;
}
  </style>
</head>
<body>
  <nav class="navbar is-transparent is-fixed-top">
  <div class="container">
    <div class="navbar-brand">
      <a href="#">Kelompok 7</a>
      <a role="button" class="navbar-burger" aria-label="menu" aria-expanded="false" data-target="navbarMenu">
        <span aria-hidden="true"></span>
        <span aria-hidden="true"></span>
        <span aria-hidden="true"></span>
      </a>
    </div>
    <div id="navbarMenu" class="navbar-menu">
      <div class="navbar-end">
        <a class="navbar-item" href="#statistik" data-section="statistik">Statistik</a>
        <a class="navbar-item" href="#rsvp" data-section="rsvp">RSVP</a>
        <a class="navbar-item" href="#wishes" data-section="wishes">Wishes</a>
        <a class="navbar-item" href="#pengantin" data-section="pengantin">Pengantin</a>
        <a class="navbar-item" href="#galeri" data-section="galeri">Galeri</a>
        <a class="navbar-item" href="#lokasi" data-section="lokasi">Lokasi</a>
        <a class="navbar-item" href="#story" data-section="story">Story</a>
        <a class="navbar-item" href="#hadiah" data-section="hadiah">Hadiah</a>
        <a class="navbar-item" href="#live" data-section="live">Live</a>
        
        <!-- PROFIL ADMIN PALING KANAN -->
        <div class="navbar-item has-dropdown is-hoverable is-align-items-center" style="margin-left: 1rem;">
          <a class="navbar-link" style="padding:0; display: flex; align-items: center;">
            <figure class="image is-32x32" style="margin-right:10px; margin-bottom:0;">
              @php
                $adminPhoto = session('admin_photo') ?? null;
              @endphp
              @if($adminPhoto && file_exists(public_path('img/'.$adminPhoto)))
                <img class="is-rounded" src="{{ asset('img/'.$adminPhoto) }}" alt="Profile" style="object-fit:cover;">
              @else
                <span class="icon is-large" style="display:flex;align-items:center;justify-content:center;width:32px;height:32px;background:#e3e9ef;border-radius:50%;">
                  <i class="fas fa-user" style="font-size:18px;color:#b0b7c3;"></i>
                </span>
              @endif
            </figure>
            <span style="font-weight:600; margin-right:8px;">{{ session('admin_name') ?? 'Admin' }}</span>
            <span class="icon" style="margin-left:2px;"><i class="fas fa-angle-down"></i></span>
          </a>
          <div class="navbar-dropdown is-right">
            <a class="navbar-item" href="#" onclick="showProfileSection(event)">
  <span class="icon"><i class="fas fa-user"></i></span>
  <span>Profile</span>
</a>
            <hr class="navbar-divider">
            <a class="navbar-item" href="#" onclick="return confirmLogout(event)">
  <span class="icon"><i class="fas fa-sign-out-alt"></i></span>
  <span>Logout</span>
</a>
          </div>
        </div>
        <!-- END PROFIL ADMIN -->
      </div>
    </div>
  </div>
</nav>

  <section class="section">
    <div class="container content" id="mainContent">
      <div id="statistikSection">
  <h1 class="title">Statistik</h1>
  <p>Welcome, <b>{{ session('admin_name') ?? 'Admin' }}</b>!</p>
  <div class="columns is-multiline" style="margin-top: 2rem;">
    <div class="column is-one-quarter-desktop is-half-tablet is-full-mobile">
      <div class="box has-text-centered" style="background:#f6fafd;">
        <div style="font-size:2.5rem; font-weight:bold; color:#52b2b9;">{{ $rsvpCount }}</div>
        <div style="font-weight:600;">Total RSVP</div>
      </div>
    </div>
    <div class="column is-one-quarter-desktop is-half-tablet is-full-mobile">
      <div class="box has-text-centered" style="background:#f6fafd;">
        <div style="font-size:2.5rem; font-weight:bold; color:#52b2b9;">{{ $totalHadir }}</div>
        <div style="font-weight:600;">Tamu Akan Hadir</div>
      </div>
    </div>
    <div class="column is-one-quarter-desktop is-half-tablet is-full-mobile">
      <div class="box has-text-centered" style="background:#f6fafd;">
        <div style="font-size:2.5rem; font-weight:bold; color:#e76f51;">{{ $totalTidakHadir }}</div>
        <div style="font-weight:600;">Tamu Tidak Hadir</div>
      </div>
    </div>
    <div class="column is-one-quarter-desktop is-half-tablet is-full-mobile">
      <div class="box has-text-centered" style="background:#f6fafd;">
        <div style="font-size:2.5rem; font-weight:bold; color:#52b2b9;">{{ $jumlahOrangHadir }}</div>
        <div style="font-weight:600;">Orang Akan Hadir</div>
      </div>
    </div>
    <div class="column is-one-quarter-desktop is-half-tablet is-full-mobile">
      <div class="box has-text-centered" style="background:#f6fafd;">
        <div style="font-size:2.5rem; font-weight:bold; color:#e76f51;">{{ $jumlahOrangTidakHadir }}</div>
        <div style="font-weight:600;">Orang Tidak Hadir</div>
      </div>
    </div>
    <div class="column is-one-quarter-desktop is-half-tablet is-full-mobile">
      <div class="box has-text-centered" style="background:#f6fafd;">
        <div style="font-size:2.5rem; font-weight:bold; color:#52b2b9;">{{ $wishesCount }}</div>
        <div style="font-weight:600;">Ucapan/Wishes</div>
      </div>
    </div>
    <div class="column is-one-quarter-desktop is-half-tablet is-full-mobile">
      <div class="box has-text-centered" style="background:#f6fafd;">
        <div style="font-size:2.5rem; font-weight:bold; color:#52b2b9;">{{ $galeriCount }}</div>
        <div style="font-weight:600;">Foto Galeri</div>
      </div>
    </div>
  </div>
</div>
      <div id="rsvpSection" style="display:none;">
        <h1 class="title">RSVP Management</h1>
        <div class="field">
          <div class="control">
            <input class="input" type="text" id="searchRSVP" onkeyup="filterRSVP()" placeholder="Cari nama atau status...">
          </div>
        </div>
        <div class="mb-3" style="margin-bottom: 1rem;">
          <a href="{{ url('tambah_rsvp') }}" class="button is-primary">
            <span class="icon"><i class="fas fa-plus"></i></span>
            <span>Tambah</span>
          </a>
        </div>
        <table id="rsvpTable" class="table is-fullwidth is-striped">
          <thead>
            <tr>
              <th>No</th>
              <th>Nama</th>
              <th>Jumlah</th>
              <th>Status</th>
              <th>Aksi</th>
            </tr>
          </thead>
          <tbody>
            @forelse ($rsvps as $no => $row)
              <tr>
                <td>{{ $no + 1 }}</td>
                <td>{{ $row->nama }}</td>
                <td>{{ $row->jumlah }}</td>
                <td>{!! $row->konfirm == 1 ? '✅ Hadir' : '❌ Tidak Hadir' !!}</td>
                <td>
                  <a class="button is-small is-info" href="{{ url('edit_rsvp/'.$row->rsvp) }}">Edit</a>
                  <button class="button is-small is-danger" onclick="confirmDelete({{ $row->rsvp }})">Hapus</button>
                </td>
              </tr>
            @empty
              <tr><td colspan="5">Belum ada data RSVP</td></tr>
            @endforelse
          </tbody>
        </table>
      </div>

      <div id="wishesSection" style="display:none;">
  <h1 class="title">Wishes Management</h1>
  <form method="POST" action="{{ url('/dashboard/wishes') }}">
    @csrf
    <div class="field">
      <label class="label">Nama</label>
      <div class="control">
        <input class="input" type="text" name="nama" required>
      </div>
    </div>
    <div class="field">
      <label class="label">Ucapan</label>
      <div class="control">
        <textarea class="textarea" name="ucapan" required></textarea>
      </div>
    </div>
    <button class="button is-primary" type="submit">Tambah Ucapan</button>
  </form>
  <hr>
  <table class="table is-fullwidth is-striped">
    <thead>
      <tr>
        <th>No</th>
        <th>Nama</th>
        <th>Ucapan</th>
        <th>Aksi</th>
      </tr>
    </thead>
    <tbody>
      @forelse($wishes as $no => $wish)
        <tr>
          <td>{{ $no+1 }}</td>
          <td>{{ $wish->nama }}</td>
          <td>{{ $wish->ucapan }}</td>
          <td>
            <a href="{{ url('hapus_wish/'.$wish->id) }}" class="button is-danger is-small">Hapus</a>
          </td>
        </tr>
      @empty
        <tr><td colspan="4">Belum ada ucapan</td></tr>
      @endforelse
    </tbody>
  </table>
</div>

<div id="pengantinSection" style="display:none;">
  <h1 class="title">Edit Data Pengantin</h1>
  <form method="POST" action="{{ url('/dashboard/pengantin') }}" enctype="multipart/form-data">
    @csrf
    <div class="field">
  <label class="label">Nama Lengkap Pria</label>
  <div class="control">
    <input class="input" type="text" name="nama_lengkap_pria" value="{{ $pengantin->nama_lengkap_pria ?? '' }}">
  </div>
</div>
    <div class="field">
      <label class="label">Nama Pendek Pria</label>
      <div class="control">
        <input class="input" type="text" name="nama_pria" value="{{ $pengantin->nama_pria ?? '' }}" required>
      </div>
    </div>
    <div class="field">
  <label class="label">Nama Ayah Pria</label>
  <div class="control">
    <input class="input" type="text" name="nama_ayah_pria" value="{{ $pengantin->nama_ayah_pria ?? '' }}">
  </div>
</div>
<div class="field">
  <label class="label">Nama Ibu Pria</label>
  <div class="control">
    <input class="input" type="text" name="nama_ibu_pria" value="{{ $pengantin->nama_ibu_pria ?? '' }}">
  </div>
</div>
    <div class="field">
      <label class="label">Foto Pria</label>
      <div class="control">
        <input class="input" type="file" name="foto_pria">
        @if($pengantin && $pengantin->foto_pria)
          <img src="{{ asset('img/'.$pengantin->foto_pria) }}" width="100" style="margin-top:10px;">
        @endif
      </div>
    </div>
    <div class="field">
  <label class="label">Nama Lengkap Wanita</label>
  <div class="control">
    <input class="input" type="text" name="nama_lengkap_wanita" value="{{ $pengantin->nama_lengkap_wanita ?? '' }}">
  </div>
</div>
    <div class="field">
      <label class="label">Nama Pendek Wanita</label>
      <div class="control">
        <input class="input" type="text" name="nama_wanita" value="{{ $pengantin->nama_wanita ?? '' }}" required>
      </div>
    </div>
    <div class="field">
  <label class="label">Nama Ayah Wanita</label>
  <div class="control">
    <input class="input" type="text" name="nama_ayah_wanita" value="{{ $pengantin->nama_ayah_wanita ?? '' }}">
  </div>
</div>
<div class="field">
  <label class="label">Nama Ibu Wanita</label>
  <div class="control">
    <input class="input" type="text" name="nama_ibu_wanita" value="{{ $pengantin->nama_ibu_wanita ?? '' }}">
  </div>
</div>
    <div class="field">
      <label class="label">Foto Wanita</label>
      <div class="control">
        <input class="input" type="file" name="foto_wanita">
        @if($pengantin && $pengantin->foto_wanita)
          <img src="{{ asset('img/'.$pengantin->foto_wanita) }}" width="100" style="margin-top:10px;">
        @endif
      </div>
    </div>
    <button class="button is-primary" type="submit">Simpan</button>
  </form>
</div>
<div id="galeriSection" style="display:none;">
  <h1 class="title">Galeri Pengantin</h1>
  <form method="POST" action="{{ url('/dashboard/galeri') }}" enctype="multipart/form-data">
    @csrf
    <div class="field">
      <label class="label">Foto</label>
      <div class="control">
        <input class="input" type="file" name="file" required>
      </div>
    </div>
    <div class="field">
      <label class="label">Caption (opsional)</label>
      <div class="control">
        <input class="input" type="text" name="caption">
      </div>
    </div>
    <button class="button is-primary" type="submit">Upload</button>
  </form>
  <hr>
  <div class="columns is-multiline">
  @foreach($galeri as $foto)
    <div class="column is-one-quarter-desktop is-one-third-tablet is-half-mobile fade-in">
      <div style="display: flex; flex-direction: column; align-items: center; height: 100%;">
        <figure class="image" style="width: 100%; max-width: 320px;">
          <img src="{{ asset('galeri/'.$foto->file) }}" alt="Galeri"
               style="width: 100%; height: 180px; object-fit: cover; border-radius: 10px; box-shadow: 0 2px 8px rgba(0,0,0,0.08);">
        </figure>
        @if($foto->caption)
          <p class="has-text-centered mt-2" style="width:100%;">{{ $foto->caption }}</p>
        @endif
        <form method="POST" action="{{ url('/dashboard/galeri/delete/'.$foto->id) }}" style="margin-top:5px; display: flex; justify-content: center; width:100%;">
          @csrf
          <button class="button is-danger is-small" onclick="return confirm('Hapus foto ini?')">Hapus</button>
        </form>
      </div>
    </div>
  @endforeach
</div>
</div>
<div id="lokasiSection" style="display:none;">
  <h1 class="title">Edit Lokasi Pernikahan</h1>
  <form method="POST" action="{{ url('/dashboard/lokasi') }}">
    @csrf
    <div class="field">
      <label class="label">Alamat Lengkap</label>
      <div class="control">
        <input class="input" type="text" name="alamat_lokasi" value="{{ $pengantin->alamat_lokasi ?? '' }}">
      </div>
    </div>
    <div class="field">
      <label class="label">Link Google Maps</label>
      <div class="control">
        <input class="input" type="text" name="link_maps" value="{{ $pengantin->link_maps ?? '' }}">
      </div>
    </div>
    <div class="field">
      <label class="label">Embed Maps (iframe)</label>
      <div class="control">
        <textarea class="textarea" name="embed_maps">{{ $pengantin->embed_maps ?? '' }}</textarea>
      </div>
    </div>
    <button class="button is-primary" type="submit">Simpan</button>
  </form>
</div>
<div id="storySection" style="display:none;">
  <h1 class="title">Timeline Story</h1>
  <form method="POST" action="{{ url('/dashboard/story') }}" enctype="multipart/form-data">
    @csrf
    <div class="field">
      <label class="label">Judul</label>
      <div class="control">
        <input class="input" type="text" name="judul" required>
      </div>
    </div>
    <div class="field">
      <label class="label">Tanggal</label>
      <div class="control">
        <input class="input" type="date" name="tanggal" required>
      </div>
    </div>
    <div class="field">
      <label class="label">Deskripsi</label>
      <div class="control">
        <textarea class="textarea" name="deskripsi" required></textarea>
      </div>
    </div>
    <div class="field">
      <label class="label">Gambar (opsional)</label>
      <div class="control">
        <input class="input" type="file" name="gambar">
      </div>
    </div>
    <button class="button is-primary" type="submit">Tambah Story</button>
  </form>
  <hr>
  <div>
    @foreach($story as $item)
      <div style="margin-bottom: 2rem;">
        @if($item->gambar)
          <img src="{{ asset('story/'.$item->gambar) }}" alt="Story" style="max-width:120px;">
        @endif
        <h3>{{ $item->judul }}</h3>
        <small>{{ \Carbon\Carbon::parse($item->tanggal)->format('d F Y') }}</small>
        <p>{{ $item->deskripsi }}</p>
        <form method="POST" action="{{ url('/dashboard/story/delete/'.$item->id) }}">
          @csrf
          <button class="button is-danger is-small" onclick="return confirm('Hapus story ini?')">Hapus</button>
        </form>
      </div>
    @endforeach
  </div>
</div>
<div id="hadiahSection" style="display:none;">
  <h1 class="title">Hadiah</h1>
  <form method="POST" action="{{ url('/dashboard/hadiah') }}" enctype="multipart/form-data">
    @csrf
    <div class="field">
      <label class="label">Nama Bank</label>
      <div class="control">
        <input class="input" type="text" name="nama_bank" value="{{ $hadiah->nama_bank ?? '' }}">
      </div>
    </div>
    <div class="field">
      <label class="label">Nama Pemilik Rekening</label>
      <div class="control">
        <input class="input" type="text" name="nama_pemilik" value="{{ $hadiah->nama_pemilik ?? '' }}">
      </div>
    </div>
    <div class="field">
      <label class="label">No. Rekening</label>
      <div class="control">
        <input class="input" type="text" name="no_rekening" value="{{ $hadiah->no_rekening ?? '' }}">
      </div>
    </div>
    <div class="field">
      <label class="label">QRIS (gambar)</label>
      <div class="control">
        <input class="input" type="file" name="qris">
        @if($hadiah && $hadiah->qris)
          <img src="{{ asset('img/'.$hadiah->qris) }}" width="150" style="margin-top:10px;">
        @endif
      </div>
    </div>
    <div class="field">
      <label class="label">Catatan (opsional)</label>
      <div class="control">
        <textarea class="textarea" name="catatan">{{ $hadiah->catatan ?? '' }}</textarea>
      </div>
    </div>
    <button class="button is-primary" type="submit">Simpan</button>
  </form>
</div>
<div id="liveSection" style="display:none;">
  <h1 class="title">Live Streaming</h1>
  <form method="POST" action="{{ url('/dashboard/live') }}">
    @csrf
    <div class="field">
      <label class="label">Judul Live</label>
      <div class="control">
        <input class="input" type="text" name="judul" value="{{ $live->judul ?? '' }}">
      </div>
    </div>
    <div class="field">
      <label class="label">Link Live (YouTube/Zoom)</label>
      <div class="control">
        <input class="input" type="text" name="link" value="{{ $live->link ?? '' }}">
      </div>
    </div>
    <div class="field">
      <label class="label">Embed Iframe (opsional)</label>
      <div class="control">
        <textarea class="textarea" name="embed">{{ $live->embed ?? '' }}</textarea>
      </div>
    </div>
    <button class="button is-primary" type="submit">Simpan</button>
  </form>
</div>
<div id="profileSection" style="display:none;">
  <h1 class="title">Profil Admin</h1>
  <form method="POST" action="{{ url('/dashboard/profile') }}" enctype="multipart/form-data">
    @csrf
    <div class="field">
      <label class="label">Foto Profil</label>
      <div class="control">
        @php
          $adminPhoto = session('admin_photo') ?? null;
        @endphp
        @if($adminPhoto && file_exists(public_path('img/'.$adminPhoto)))
          <img src="{{ asset('img/'.$adminPhoto) }}" alt="Profile" style="width:80px;height:80px;border-radius:50%;object-fit:cover;margin-bottom:10px;">
        @else
          <span class="icon is-large" style="display:inline-flex;align-items:center;justify-content:center;width:80px;height:80px;background:#e3e9ef;border-radius:50%;margin-bottom:10px;">
            <i class="fas fa-user" style="font-size:32px;color:#b0b7c3;"></i>
          </span>
        @endif
        <input class="input" type="file" name="foto" accept="image/*">
      </div>
    </div>
    <div class="field">
      <label class="label">Nama</label>
      <div class="control">
        <input class="input" type="text" name="name" value="{{ session('admin_name') }}" required>
      </div>
    </div>
    <div class="field">
      <label class="label">Email</label>
      <div class="control">
        <input class="input" type="email" name="email" value="{{ session('admin_email') }}" required>
      </div>
    </div>
    <div class="field">
      <label class="label">Password Baru <span class="has-text-grey-light">(opsional)</span></label>
      <div class="control">
        <input class="input" type="password" name="password" placeholder="Kosongkan jika tidak ingin ganti">
      </div>
    </div>
    <div class="field">
      <button class="button is-primary is-fullwidth" type="submit">Update Profil</button>
    </div>
  </form>
</div>
  </section>

  <script>
    document.addEventListener('DOMContentLoaded', () => {
  const burger = document.querySelector('.navbar-burger');
  const menu = document.querySelector('.navbar-menu');
  burger.addEventListener('click', () => {
    burger.classList.toggle('is-active');
    menu.classList.toggle('is-active');
  });

  const statistikSection = document.getElementById('statistikSection');
  const rsvpSection = document.getElementById('rsvpSection');
  const wishesSection = document.getElementById('wishesSection');
  const pengantinSection = document.getElementById('pengantinSection');
  const galeriSection = document.getElementById('galeriSection');
  const lokasiSection = document.getElementById('lokasiSection');
  const storySection = document.getElementById('storySection');
  const hadiahSection = document.getElementById('hadiahSection');
  const navbarItems = document.querySelectorAll('.navbar-item');
  const profileSection = document.getElementById('profileSection');

  navbarItems.forEach(item => {
    item.addEventListener('click', (e) => {
      e.preventDefault();
      const section = item.getAttribute('data-section');
      // Sembunyikan semua section
      statistikSection.style.display = 'none';
      rsvpSection.style.display = 'none';
      wishesSection.style.display = 'none';
      pengantinSection.style.display = 'none';
      galeriSection.style.display = 'none';
      lokasiSection.style.display = 'none';
      storySection.style.display = 'none';
      hadiahSection.style.display = 'none';
      liveSection.style.display = 'none';
      // Tampilkan section yang dipilih
      if (section === 'statistik') statistikSection.style.display = '';
      if (section === 'rsvp') rsvpSection.style.display = '';
      if (section === 'wishes') wishesSection.style.display = '';
      if (section === 'pengantin') pengantinSection.style.display = '';
      if (section === 'galeri') galeriSection.style.display = '';
      if (section === 'lokasi') lokasiSection.style.display = '';
      if (section === 'story') storySection.style.display = '';
      if (section === 'hadiah') hadiahSection.style.display = '';
      if (section === 'live') liveSection.style.display = '';
    });
  });

      
    });
    function showProfileSection(e) {
  e.preventDefault();
  // Sembunyikan semua section lain
  statistikSection.style.display = 'none';
  rsvpSection.style.display = 'none';
  wishesSection.style.display = 'none';
  pengantinSection.style.display = 'none';
  galeriSection.style.display = 'none';
  lokasiSection.style.display = 'none';
  storySection.style.display = 'none';
  hadiahSection.style.display = 'none';
  liveSection.style.display = 'none';
  // Tampilkan profile
  profileSection.style.display = '';
}
    function filterRSVP() {
      const input = document.getElementById('searchRSVP');
      const filter = input.value.toUpperCase();
      const table = document.getElementById('rsvpTable');
      const tr = table.getElementsByTagName('tr');

      for (let i = 1; i < tr.length; i++) {
        const tdNama = tr[i].getElementsByTagName('td')[1];
        const tdStatus = tr[i].getElementsByTagName('td')[3];

        if (tdNama && tdStatus) {
          const txtValueNama = tdNama.textContent || tdNama.innerText;
          const txtValueStatus = tdStatus.textContent || tdStatus.innerText;

          if (txtValueNama.toUpperCase().includes(filter) ||
              txtValueStatus.toUpperCase().includes(filter)) {
            tr[i].style.display = '';
          } else {
            tr[i].style.display = 'none';
          }
        }
      }
    }

    function confirmDelete(id) {
      if (confirm('Apakah Anda yakin ingin menghapus data ini?')) {
        window.location.href = '{{ url("hapus_rsvp") }}/' + id;
      }
    }

    
  </script>

 @if (request()->is('dashboard/wishes'))
<script>
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('wishesSection')) {
        document.getElementById('rsvpSection').style.display = 'none';
        document.getElementById('wishesSection').style.display = '';
    }
});
</script>
@endif

@if(session('success'))
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    Swal.fire({
        icon: 'success',
        title: 'Berhasil!',
        text: "{{ session('success') }}",
        confirmButtonText: 'OK'
    });
    // Tampilkan tab sesuai route
    @if (request()->is('dashboard/wishes'))
        // Tampilkan wishes
        if (document.getElementById('wishesSection')) {
            document.getElementById('rsvpSection').style.display = 'none';
            document.getElementById('wishesSection').style.display = '';
            document.getElementById('pengantinSection').style.display = 'none';
            if(document.getElementById('galeriSection')) document.getElementById('galeriSection').style.display = 'none';
            if(document.getElementById('lokasiSection')) document.getElementById('lokasiSection').style.display = 'none';
            if(document.getElementById('storySection')) document.getElementById('storySection').style.display = 'none';
            if(document.getElementById('hadiahSection')) document.getElementById('hadiahSection').style.display = 'none';
        }
    @elseif (request()->is('dashboard'))
        // Tampilkan pengantin (jika submit dari pengantin)
        if (document.getElementById('pengantinSection')) {
            document.getElementById('rsvpSection').style.display = 'none';
            document.getElementById('wishesSection').style.display = 'none';
            document.getElementById('pengantinSection').style.display = '';
            if(document.getElementById('galeriSection')) document.getElementById('galeriSection').style.display = 'none';
            if(document.getElementById('lokasiSection')) document.getElementById('lokasiSection').style.display = 'none';
            if(document.getElementById('storySection')) document.getElementById('storySection').style.display = 'none';
            if(document.getElementById('hadiahSection')) document.getElementById('hadiahSection').style.display = 'none';
        }
    @endif
});
</script>
@endif
<script>
function confirmLogout(e) {
  if(confirm('Yakin ingin logout?')) {
    window.location.href = "{{ url('/logout') }}";
  }
  return false;
}
</script>

  @include('footer')
</body>
</html>