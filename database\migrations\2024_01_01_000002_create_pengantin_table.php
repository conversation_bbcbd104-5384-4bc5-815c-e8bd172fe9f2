<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pengantin', function (Blueprint $table) {
            $table->id();
            $table->string('nama_pria')->nullable();
            $table->string('nama_lengkap_pria')->nullable();
            $table->string('ortu_pria')->nullable();
            $table->string('nama_ayah_pria')->nullable();
            $table->string('nama_ibu_pria')->nullable();
            $table->string('foto_pria')->nullable();
            $table->string('nama_wanita')->nullable();
            $table->string('nama_lengkap_wanita')->nullable();
            $table->string('ortu_wanita')->nullable();
            $table->string('nama_ayah_wanita')->nullable();
            $table->string('nama_ibu_wanita')->nullable();
            $table->string('foto_wanita')->nullable();
            $table->text('alamat_lokasi')->nullable();
            $table->text('link_maps')->nullable();
            $table->text('embed_maps')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pengantin');
    }
};
