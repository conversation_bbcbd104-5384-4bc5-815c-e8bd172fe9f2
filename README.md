# Wedding Invitation Management System

A modern, elegant wedding invitation website built with Laravel 9. This system allows couples to create beautiful digital wedding invitations with RSVP management, photo galleries, love stories, and more.

## Features

### Public Features
- **Beautiful Wedding Invitation Page** - Responsive design with elegant animations
- **RSVP System** - Guests can confirm attendance and specify number of attendees
- **Wishes & Messages** - Guests can leave congratulatory messages
- **Photo Gallery** - Display beautiful wedding photos
- **Love Story Timeline** - Share your journey as a couple
- **Gift Information** - Bank details and QR codes for digital gifts
- **Live Streaming** - Embed live wedding ceremony streams

### Admin Features
- **Dashboard** - Complete overview with statistics
- **RSVP Management** - View, edit, and manage guest responses
- **Content Management** - Update couple information, photos, and details
- **Gallery Management** - Upload and organize wedding photos
- **Story Management** - Create and edit love story timeline
- **Gift Management** - Update bank details and QR codes
- **Live Stream Management** - Configure streaming links and embeds

## Technology Stack

- **Backend**: Laravel 9 (PHP 8.0+)
- **Frontend**: Blade Templates, Bootstrap, JavaScript
- **Database**: MySQL
- **Image Processing**: Intervention Image
- **File Storage**: Local storage with organized directories

## Requirements

- PHP 8.0 or higher
- Composer
- Node.js & NPM
- MySQL 5.7+ or MariaDB
- Web server (Apache/Nginx) or use `php artisan serve`

## Installation

### Quick Setup (Recommended)

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd undanganayfaahro
   ```

2. **Run the setup script**

   **For Windows:**
   ```cmd
   setup.bat
   ```

   **For Linux/Mac:**
   ```bash
   chmod +x setup.sh
   ./setup.sh
   ```

3. **Configure your database**
   - Edit `.env` file with your database credentials
   - Run migrations again if needed: `php artisan migrate`

4. **Start the development server**
   ```bash
   php artisan serve
   ```

5. **Access the application**
   - Website: http://localhost:8000
   - Admin Login: http://localhost:8000/login
   - Default credentials: <EMAIL> / password123

### Manual Installation

1. **Install dependencies**
   ```bash
   composer install
   npm install
   ```

2. **Environment setup**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

3. **Configure database in .env**
   ```env
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=your_database_name
   DB_USERNAME=your_username
   DB_PASSWORD=your_password
   ```

4. **Run migrations and seeders**
   ```bash
   php artisan migrate
   php artisan db:seed
   ```

5. **Create storage directories**
   ```bash
   php artisan storage:link
   mkdir -p public/img public/galeri public/story
   ```

## Configuration

### Database Setup

1. Create a MySQL database for your wedding website
2. Update the `.env` file with your database credentials
3. Run migrations to create the required tables:
   ```bash
   php artisan migrate
   ```

### Admin Account

The system comes with a default admin account:
- **Email**: <EMAIL>
- **Password**: password123

**Important**: Change these credentials after first login for security.

### File Permissions

Ensure the following directories are writable:
```bash
chmod -R 775 storage
chmod -R 775 bootstrap/cache
chmod -R 775 public/img
chmod -R 775 public/galeri
chmod -R 775 public/story
```

## Usage

### Admin Dashboard

1. **Login** to the admin panel at `/login`
2. **Dashboard Overview** - View RSVP statistics and recent activities
3. **Manage Content**:
   - Update couple information and photos
   - Add/edit love story timeline
   - Upload gallery photos
   - Configure gift information
   - Set up live streaming details

### RSVP Management

- View all guest responses
- Edit guest information
- Track attendance numbers
- Export guest lists

### Content Management

- **Couple Info**: Names, photos, parents' names
- **Location**: Wedding venue details and maps
- **Gallery**: Upload and organize photos
- **Story**: Create timeline of your relationship
- **Gifts**: Bank details and QR codes
- **Live Stream**: Embed streaming links

## File Structure

```
app/
├── Http/
│   ├── Controllers/
│   │   ├── AuthController.php      # Authentication logic
│   │   ├── DashboardController.php # Admin dashboard
│   │   ├── HomeController.php      # Public pages
│   │   ├── RsvpController.php      # RSVP management
│   │   └── WishController.php      # Wishes management
│   ├── Middleware/
│   │   └── AdminAuth.php           # Admin authentication
│   └── Requests/                   # Form validation
├── Models/                         # Eloquent models
├── Services/
│   └── FileUploadService.php       # File handling
database/
├── migrations/                     # Database schema
└── seeders/                        # Initial data
public/
├── img/                           # Couple photos
├── galeri/                        # Gallery photos
└── story/                         # Story images
```

## Security Features

- **CSRF Protection** - All forms protected against CSRF attacks
- **Input Validation** - Comprehensive validation for all inputs
- **File Upload Security** - Image validation and secure storage
- **Admin Authentication** - Session-based admin authentication
- **SQL Injection Prevention** - Eloquent ORM prevents SQL injection

## Customization

### Styling
- Modify `public/style.css` for custom styling
- Update Blade templates in `resources/views/`

### Features
- Add new models and migrations for additional features
- Extend controllers for custom functionality
- Create new middleware for additional security

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check `.env` database credentials
   - Ensure MySQL service is running
   - Verify database exists

2. **File Upload Issues**
   - Check directory permissions
   - Ensure directories exist: `public/img`, `public/galeri`, `public/story`

3. **Composer/NPM Errors**
   - Run `composer install --no-dev` for production
   - Clear cache: `php artisan cache:clear`

### Performance Optimization

```bash
# For production deployment
php artisan config:cache
php artisan route:cache
php artisan view:cache
composer install --optimize-autoloader --no-dev
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is open-sourced software licensed under the [MIT license](https://opensource.org/licenses/MIT).

## Support

For support and questions:
- Create an issue on GitHub
- Check the Laravel documentation for framework-related questions
- Review the code comments for implementation details

---

**Note**: This system has been completely refactored from the original codebase to follow Laravel best practices, improve security, and enhance maintainability.
