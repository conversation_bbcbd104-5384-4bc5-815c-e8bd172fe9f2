<!-- filepath: resources/views/profile.blade.php -->
<!DOCTYPE html>
<html>
<head>
  <title>Profil Admin</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
  <link href="https://fonts.googleapis.com/css2?family=Work+Sans:wght@100;300;400;600;700&display=swap" rel="stylesheet">
  <style>
    body { background: #f7fafc; font-family: 'Work Sans', Arial, sans-serif; }
    .profile-container {
      max-width: 420px;
      margin: 4rem auto;
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 4px 24px rgba(0,0,0,0.08);
      padding: 2.5rem 2rem;
    }
    .profile-title {
      font-weight: bold;
      font-size: 2rem;
      color: #52b2b9;
      margin-bottom: 1.5rem;
      text-align: center;
    }
  </style>
</head>
<body>
  <div class="profile-container">
    <div class="profile-title">Profil Admin</div>
    @if(session('success'))
      <div class="notification is-success is-light">{{ session('success') }}</div>
    @endif
    <form method="POST" action="{{ url('/dashboard/profile') }}">
      @csrf
      <div class="field">
        <label class="label">Nama</label>
        <div class="control">
          <input class="input" type="text" name="name" value="{{ $admin->name }}" required>
        </div>
      </div>
      <div class="field">
        <label class="label">Email</label>
        <div class="control">
          <input class="input" type="email" name="email" value="{{ $admin->email }}" required>
        </div>
      </div>
      <div class="field">
        <label class="label">Password Baru <span class="has-text-grey-light">(opsional)</span></label>
        <div class="control">
          <input class="input" type="password" name="password" placeholder="Kosongkan jika tidak ingin ganti">
        </div>
      </div>
      <div class="field">
        <button class="button is-primary is-fullwidth" type="submit">Update Profil</button>
      </div>
    </form>
    <div class="has-text-centered" style="margin-top:1.5rem;">
      <a href="{{ url('/dashboard') }}" class="button is-light">Kembali ke Dashboard</a>
    </div>
  </div>
</body>
</html>