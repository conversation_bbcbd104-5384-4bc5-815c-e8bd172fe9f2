<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreRsvpRequest;
use App\Models\Rsvp;
use Illuminate\Http\Request;

class RsvpController extends Controller
{
    /**
     * Display a listing of RSVPs
     */
    public function index()
    {
        $rsvps = Rsvp::latest()->get();
        $rsvpCount = $rsvps->count();
        $totalHadir = $rsvps->where('konfirm', true)->count();
        $totalTidakHadir = $rsvps->where('konfirm', false)->count();
        $jumlahOrangHadir = $rsvps->where('konfirm', true)->sum('jumlah');
        $jumlahOrangTidakHadir = $rsvps->where('konfirm', false)->sum('jumlah');

        return view('dashboard.rsvp.index', compact(
            'rsvps', 'rsvpCount', 'totalHadir', 'totalTidakHadir',
            'jumlahOrangHadir', 'jumlahOrangTidakHadir'
        ));
    }

    /**
     * Show the form for creating a new RSVP
     */
    public function create()
    {
        return view('tambah_rsvp');
    }

    /**
     * Store a newly created RSVP
     */
    public function store(StoreRsvpRequest $request)
    {
        Rsvp::create($request->validated());

        // Handle AJAX requests
        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'RSVP berhasil disimpan!'
            ]);
        }

        // Handle regular form submissions
        if ($request->input('from') === 'index') {
            return redirect('/')->with('success', 'RSVP berhasil disimpan!');
        }

        return redirect()->route('dashboard')->with('success', 'RSVP berhasil disimpan!');
    }

    /**
     * Show the form for editing the specified RSVP
     */
    public function edit($id)
    {
        $rsvp = Rsvp::findOrFail($id);
        return view('edit_rsvp', compact('rsvp'));
    }

    /**
     * Update the specified RSVP
     */
    public function update(StoreRsvpRequest $request, $id)
    {
        $rsvp = Rsvp::findOrFail($id);
        $rsvp->update($request->validated());

        return redirect()->route('dashboard')->with('success', 'Data berhasil diupdate');
    }

    /**
     * Remove the specified RSVP
     */
    public function destroy($id)
    {
        $rsvp = Rsvp::findOrFail($id);
        $rsvp->delete();

        return redirect()->route('dashboard')->with('success', 'Data berhasil dihapus');
    }
}
