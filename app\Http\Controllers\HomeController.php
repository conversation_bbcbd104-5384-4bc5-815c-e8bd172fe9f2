<?php

namespace App\Http\Controllers;

use App\Models\Pengantin;
use App\Models\Wish;
use App\Models\Galeri;
use App\Models\Story;
use App\Models\Hadiah;
use App\Models\Live;

class HomeController extends Controller
{
    /**
     * Display the wedding invitation homepage
     */
    public function index()
    {
        $pengantin = Pengantin::first();
        $wishes = Wish::latest()->get();
        $galeri = Galeri::latest()->get();
        $story = Story::orderBy('tanggal')->get();
        $hadiah = Hadiah::first();
        $live = Live::first();

        return view('index', compact('pengantin', 'wishes', 'galeri', 'story', 'hadiah', 'live'));
    }
}
